<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI流式聊天测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .chat-container {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            height: 400px;
            overflow-y: auto;
        }
        .message {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .user-message {
            background-color: #e6f7ff;
            text-align: right;
        }
        .ai-message {
            background-color: #f5f5f5;
        }
        .input-container {
            display: flex;
        }
        #message-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-right: 10px;
        }
        button {
            padding: 10px 20px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #40a9ff;
        }
        button:disabled {
            background-color: #d9d9d9;
            cursor: not-allowed;
        }
        .loading {
            color: #999;
            font-style: italic;
        }
    </style>
</head>
<body>
    <h1>AI流式聊天测试</h1>
    <div class="chat-container" id="chat-container"></div>
    <div class="input-container">
        <input type="text" id="message-input" placeholder="请输入消息..." />
        <button id="send-button">发送</button>
    </div>

    <script>
        const chatContainer = document.getElementById('chat-container');
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        
        let conversationId = '';
        let isWaiting = false;
        
        // 添加用户消息到聊天窗口
        function addUserMessage(message) {
            const messageElement = document.createElement('div');
            messageElement.className = 'message user-message';
            messageElement.textContent = message;
            chatContainer.appendChild(messageElement);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        // 添加AI消息到聊天窗口
        function addAiMessage(message) {
            const messageElement = document.createElement('div');
            messageElement.className = 'message ai-message';
            messageElement.textContent = message;
            chatContainer.appendChild(messageElement);
            chatContainer.scrollTop = chatContainer.scrollHeight;
            return messageElement;
        }
        
        // 更新AI消息
        function updateAiMessage(element, message) {
            element.textContent = message;
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        // 发送消息
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || isWaiting) return;
            
            // 清空输入框并禁用发送按钮
            messageInput.value = '';
            isWaiting = true;
            sendButton.disabled = true;
            
            // 添加用户消息
            addUserMessage(message);
            
            // 创建加载提示
            const loadingElement = addAiMessage('正在思考...');
            
            try {
                // 准备请求数据
                const requestData = {
                    query: message,
                    response_mode: 'streaming',
                    user: 'web-user'
                };
                
                // 如果有会话ID，则添加到请求中
                if (conversationId) {
                    requestData.conversation_id = conversationId;
                }
                
                // 使用fetch进行流式请求
                const url = '/api/ai/chat/messages/stream';
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                // 处理流式响应
                let aiResponse = '';
                let buffer = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value, { stream: true });
                    buffer += chunk;

                    // 处理SSE消息
                    while (buffer.includes('\n\n')) {
                        const index = buffer.indexOf('\n\n');
                        const event = buffer.substring(0, index);
                        buffer = buffer.substring(index + 2);

                        if (event.startsWith('data:')) {
                            const eventData = event.substring(5).trim();
                            if (eventData && eventData !== '') {
                                try {
                                    const data = JSON.parse(eventData);

                                    if (data.event === 'message' && data.answer) {
                                        aiResponse += data.answer;
                                        updateAiMessage(loadingElement, aiResponse);
                                    }

                                    if (data.conversation_id && !conversationId) {
                                        conversationId = data.conversation_id;
                                    }

                                    if (data.event === 'workflow_finished' || data.finished) {
                                        // 从完整数据中提取最终答案
                                        if (data.data && data.data.outputs && data.data.outputs.answer) {
                                            const finalAnswer = data.data.outputs.answer;
                                            // 移除<think>标签内容
                                            const cleanAnswer = finalAnswer.replace(/<think>[\s\S]*?<\/think>/g, '').trim();
                                            if (cleanAnswer && cleanAnswer !== aiResponse) {
                                                updateAiMessage(loadingElement, cleanAnswer);
                                            }
                                        }
                                        isWaiting = false;
                                        sendButton.disabled = false;
                                        return;
                                    }

                                    if (data.event === 'error') {
                                        updateAiMessage(loadingElement, '错误: ' + (data.error || '未知错误'));
                                        isWaiting = false;
                                        sendButton.disabled = false;
                                        return;
                                    }
                                } catch (e) {
                                    // 忽略空数据或无效JSON的解析错误
                                    if (eventData.length > 10) {
                                        console.warn('解析JSON失败:', e.message, '数据:', eventData.substring(0, 100) + '...');
                                    }
                                }
                            }
                        }
                    }
                }

                isWaiting = false;
                sendButton.disabled = false;
                
            } catch (error) {
                console.error('发送消息错误:', error);
                updateAiMessage(loadingElement, '发送消息失败，请重试');
                isWaiting = false;
                sendButton.disabled = false;
            }
        }
        
        // 绑定发送按钮点击事件
        sendButton.addEventListener('click', sendMessage);
        
        // 绑定输入框回车事件
        messageInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
