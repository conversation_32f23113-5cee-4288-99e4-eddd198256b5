<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flux流式输出演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            min-height: 200px;
            margin-bottom: 20px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .message {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #e6f7ff;
            border-radius: 5px;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            display: block;
            margin: 0 auto;
        }
        button:hover {
            background-color: #40a9ff;
        }
        .status {
            text-align: center;
            margin-top: 10px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Flux流式输出演示</h1>
        
        <div class="output" id="output">等待开始...</div>
        
        <button id="start-button">开始测试</button>
        
        <div class="status" id="status">准备就绪</div>
    </div>

    <script>
        const output = document.getElementById('output');
        const startButton = document.getElementById('start-button');
        const status = document.getElementById('status');
        
        // 添加消息到输出区域
        function addMessage(message) {
            const messageElement = document.createElement('div');
            messageElement.className = 'message';
            messageElement.textContent = message;
            output.appendChild(messageElement);
        }
        
        // 清空输出
        function clearOutput() {
            output.innerHTML = '';
        }
        
        // 更新状态
        function updateStatus(text) {
            status.textContent = text;
        }
        
        // 开始测试
        async function startTest() {
            startButton.disabled = true;
            clearOutput();
            updateStatus('正在连接...');
            
            try {
                const response = await fetch('/api/ai/test/flux-demo', {
                    headers: {
                        'Accept': 'text/event-stream'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                updateStatus('接收数据中...');
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value, { stream: true });
                    buffer += chunk;
                    
                    // 处理SSE消息
                    while (buffer.includes('\n\n')) {
                        const index = buffer.indexOf('\n\n');
                        const event = buffer.substring(0, index);
                        buffer = buffer.substring(index + 2);
                        
                        if (event.startsWith('data:')) {
                            const eventData = event.substring(5).trim();
                            if (eventData && eventData !== '') {
                                try {
                                    const data = JSON.parse(eventData);
                                    
                                    if (data.event === 'message' && data.answer) {
                                        addMessage(data.answer);
                                    }
                                    
                                    if (data.event === 'workflow_finished' || data.finished) {
                                        updateStatus('测试完成');
                                        startButton.disabled = false;
                                        return;
                                    }
                                } catch (e) {
                                    console.warn('解析JSON失败:', e.message);
                                    addMessage('解析错误: ' + eventData);
                                }
                            }
                        }
                    }
                }
                
                updateStatus('连接已关闭');
                startButton.disabled = false;
                
            } catch (error) {
                console.error('测试失败:', error);
                updateStatus('测试失败: ' + error.message);
                startButton.disabled = false;
            }
        }
        
        // 绑定按钮点击事件
        startButton.addEventListener('click', startTest);
    </script>
</body>
</html>
