<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Flux流式聊天测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .chat-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
            height: 400px;
            overflow-y: auto;
        }
        .message {
            margin-bottom: 15px;
            padding: 12px;
            border-radius: 8px;
            max-width: 80%;
        }
        .user-message {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-left: auto;
            text-align: right;
        }
        .ai-message {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            color: #333;
        }
        .input-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            display: flex;
            gap: 10px;
        }
        #message-input {
            flex: 1;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }
        #message-input:focus {
            border-color: #667eea;
        }
        button {
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s;
        }
        button:hover:not(:disabled) {
            transform: translateY(-2px);
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        .loading {
            color: #6c757d;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        .status {
            text-align: center;
            margin: 10px 0;
            font-size: 14px;
            color: #6c757d;
        }
        .typing-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #667eea;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 AI Flux流式聊天测试</h1>
        <p>基于Spring WebFlux Flux&lt;String&gt;的响应式流式聊天</p>
    </div>
    
    <div class="chat-container" id="chat-container">
        <div class="message ai-message">
            <strong>AI助手:</strong> 你好！我是基于Flux流式响应的AI助手。请输入消息开始对话。
        </div>
    </div>
    
    <div class="status" id="status">准备就绪</div>
    
    <div class="input-container">
        <input type="text" id="message-input" placeholder="请输入消息..." />
        <button id="send-button">发送</button>
    </div>

    <script>
        const chatContainer = document.getElementById('chat-container');
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        const status = document.getElementById('status');
        
        let conversationId = '';
        let isWaiting = false;
        
        // 添加用户消息到聊天窗口
        function addUserMessage(message) {
            const messageElement = document.createElement('div');
            messageElement.className = 'message user-message';
            messageElement.innerHTML = `<strong>你:</strong> ${message}`;
            chatContainer.appendChild(messageElement);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        // 添加AI消息到聊天窗口
        function addAiMessage(message) {
            const messageElement = document.createElement('div');
            messageElement.className = 'message ai-message';
            messageElement.innerHTML = `<strong>AI:</strong> ${message}`;
            chatContainer.appendChild(messageElement);
            chatContainer.scrollTop = chatContainer.scrollHeight;
            return messageElement;
        }
        
        // 更新AI消息
        function updateAiMessage(element, message) {
            element.innerHTML = `<strong>AI:</strong> ${message}`;
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        // 更新状态
        function updateStatus(text, isLoading = false) {
            if (isLoading) {
                status.innerHTML = `<span class="typing-indicator"></span> ${text}`;
            } else {
                status.textContent = text;
            }
        }
        
        // 发送消息
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || isWaiting) return;
            
            // 清空输入框并禁用发送按钮
            messageInput.value = '';
            isWaiting = true;
            sendButton.disabled = true;
            updateStatus('正在发送消息...', true);
            
            // 添加用户消息
            addUserMessage(message);
            
            // 创建加载提示
            const loadingElement = addAiMessage('<span class="loading">正在思考...</span>');
            
            try {
                // 准备请求数据
                const requestData = {
                    query: message,
                    response_mode: 'streaming',
                    user: 'flux-test-user'
                };
                
                // 如果有会话ID，则添加到请求中
                if (conversationId) {
                    requestData.conversation_id = conversationId;
                }
                
                updateStatus('正在连接AI服务...', true);
                
                // 发送请求并处理流式响应
                const response = await fetch('/api/ai/chat/messages/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                updateStatus('正在接收响应...', true);
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let aiResponse = '';
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value, { stream: true });
                    buffer += chunk;
                    
                    // 处理SSE消息
                    while (buffer.includes('\n\n')) {
                        const index = buffer.indexOf('\n\n');
                        const event = buffer.substring(0, index);
                        buffer = buffer.substring(index + 2);
                        
                        if (event.startsWith('data:')) {
                            const eventData = event.substring(5).trim();
                            if (eventData && eventData !== '') {
                                try {
                                    const data = JSON.parse(eventData);

                                    if (data.event === 'message' && data.answer) {
                                        aiResponse += data.answer;
                                        updateAiMessage(loadingElement, aiResponse);
                                        updateStatus('正在接收响应...', true);
                                    }

                                    if (data.conversation_id && !conversationId) {
                                        conversationId = data.conversation_id;
                                        console.log('会话ID:', conversationId);
                                    }

                                    if (data.event === 'workflow_finished' || data.finished) {
                                        // 从完整数据中提取最终答案
                                        if (data.data && data.data.outputs && data.data.outputs.answer) {
                                            const finalAnswer = data.data.outputs.answer;
                                            // 移除<think>标签内容
                                            const cleanAnswer = finalAnswer.replace(/<think>[\s\S]*?<\/think>/g, '').trim();
                                            if (cleanAnswer && cleanAnswer !== aiResponse) {
                                                updateAiMessage(loadingElement, cleanAnswer);
                                            }
                                        }
                                        updateStatus('响应完成');
                                        isWaiting = false;
                                        sendButton.disabled = false;
                                        return;
                                    }

                                    if (data.event === 'error') {
                                        const errorMsg = '错误: ' + (data.error || '未知错误');
                                        updateAiMessage(loadingElement, errorMsg);
                                        loadingElement.className = 'message ai-message error';
                                        updateStatus('发生错误');
                                        isWaiting = false;
                                        sendButton.disabled = false;
                                        return;
                                    }
                                } catch (e) {
                                    // 忽略空数据或无效JSON的解析错误
                                    if (eventData.length > 10) {
                                        console.warn('解析JSON失败:', e.message, '数据:', eventData.substring(0, 100) + '...');
                                    }
                                }
                            }
                        }
                    }
                }
                
                updateStatus('响应完成');
                isWaiting = false;
                sendButton.disabled = false;
                
            } catch (error) {
                console.error('发送消息错误:', error);
                updateAiMessage(loadingElement, '发送消息失败: ' + error.message);
                loadingElement.className = 'message ai-message error';
                updateStatus('发送失败');
                isWaiting = false;
                sendButton.disabled = false;
            }
        }
        
        // 绑定发送按钮点击事件
        sendButton.addEventListener('click', sendMessage);
        
        // 绑定输入框回车事件
        messageInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        });
        
        // 页面加载完成后聚焦输入框
        window.addEventListener('load', function() {
            messageInput.focus();
        });
    </script>
</body>
</html>
