package com.zkdm.iai.ai.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zkdm.iai.ai.config.AiConfig;
import com.zkdm.iai.ai.dto.ChatRequestDto;
import com.zkdm.iai.ai.dto.StreamResponseDto;
import com.zkdm.iai.ai.exception.AiServiceException;
import com.zkdm.iai.ai.service.IStreamChatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.CompletableFuture;

/**
 * 流式聊天服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StreamChatServiceImpl implements IStreamChatService {
    
    private final AiConfig aiConfig;
    private final ObjectMapper objectMapper;
    
    @Override
    public SseEmitter sendStreamMessage(ChatRequestDto request) {
        // 创建SSE发射器，设置超时时间为5分钟
        SseEmitter emitter = new SseEmitter(300000L);
        
        // 设置默认用户标识
        if (!StringUtils.hasText(request.getUser())) {
            request.setUser(aiConfig.getDefaultUser());
        }
        
        // 确保是流式模式
        request.setResponseMode("streaming");
        
        log.info("开始流式聊天，用户: {}, 会话ID: {}, 查询内容: {}", 
            request.getUser(), request.getConversationId(), request.getQuery());
        
        // 异步处理流式响应
        CompletableFuture.runAsync(() -> {
            try {
                processStreamResponse(request, emitter);
            } catch (Exception e) {
                log.error("流式聊天处理失败", e);
                try {
                    StreamResponseDto errorResponse = new StreamResponseDto();
                    errorResponse.setEvent("error");
                    errorResponse.setError("流式聊天处理失败: " + e.getMessage());
                    errorResponse.setFinished(true);
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data(errorResponse));
                    emitter.complete();
                } catch (IOException ioException) {
                    log.error("发送错误消息失败", ioException);
                    emitter.completeWithError(ioException);
                }
            }
        });
        
        return emitter;
    }
    
    private void processStreamResponse(ChatRequestDto request, SseEmitter emitter) throws Exception {
        HttpURLConnection connection = null;
        BufferedReader reader = null;
        
        try {
            // 创建HTTP连接
            URL url = new URL(aiConfig.getBaseUrl() + "/chat-messages");
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Authorization", "Bearer " + aiConfig.getApiKey());
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", "text/event-stream");
            connection.setDoOutput(true);
            connection.setConnectTimeout(aiConfig.getConnectTimeout());
            connection.setReadTimeout(aiConfig.getReadTimeout());
            
            // 发送请求体
            String requestBody = objectMapper.writeValueAsString(request);
            log.debug("发送请求: {}", requestBody);
            
            try (var outputStream = connection.getOutputStream()) {
                outputStream.write(requestBody.getBytes(StandardCharsets.UTF_8));
                outputStream.flush();
            }
            
            // 检查响应状态
            int responseCode = connection.getResponseCode();
            if (responseCode != 200) {
                throw new AiServiceException("HTTP请求失败，状态码: " + responseCode);
            }
            
            // 读取流式响应
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
            String line;
            StringBuilder buffer = new StringBuilder();
            
            while ((line = reader.readLine()) != null) {
                buffer.append(line).append("\n");
                
                // SSE消息以双换行符分隔
                String content = buffer.toString();
                while (content.contains("\n\n")) {
                    int index = content.indexOf("\n\n");
                    String event = content.substring(0, index);
                    content = content.substring(index + 2);
                    buffer = new StringBuilder(content);
                    
                    if (event.startsWith("data:")) {
                        String eventData = event.substring(5).trim();
                        if (!eventData.isEmpty()) {
                            processEventData(eventData, emitter);
                        }
                    }
                }
            }
            
            emitter.complete();
            log.info("流式聊天完成");
            
        } catch (Exception e) {
            log.error("处理流式响应失败", e);
            throw e;
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    log.warn("关闭reader失败", e);
                }
            }
            if (connection != null) {
                connection.disconnect();
            }
        }
    }
    
    private void processEventData(String eventData, SseEmitter emitter) throws IOException {
        try {
            JsonNode jsonNode = objectMapper.readTree(eventData);
            StreamResponseDto response = new StreamResponseDto();
            
            String event = jsonNode.path("event").asText();
            response.setEvent(event);
            
            if ("message".equals(event)) {
                response.setAnswer(jsonNode.path("answer").asText(""));
                response.setTaskId(jsonNode.path("task_id").asText());
                response.setMessageId(jsonNode.path("message_id").asText());
                response.setConversationId(jsonNode.path("conversation_id").asText());
                response.setCreatedAt(jsonNode.path("created_at").asLong());
                
                emitter.send(SseEmitter.event()
                    .name("message")
                    .data(response));
                    
            } else if ("workflow_finished".equals(event)) {
                response.setFinished(true);
                response.setData(jsonNode);
                
                emitter.send(SseEmitter.event()
                    .name("finished")
                    .data(response));
            }
            
        } catch (Exception e) {
            log.error("解析事件数据失败: {}", eventData, e);
            StreamResponseDto errorResponse = new StreamResponseDto();
            errorResponse.setEvent("error");
            errorResponse.setError("解析响应数据失败");
            emitter.send(SseEmitter.event()
                .name("error")
                .data(errorResponse));
        }
    }
}
