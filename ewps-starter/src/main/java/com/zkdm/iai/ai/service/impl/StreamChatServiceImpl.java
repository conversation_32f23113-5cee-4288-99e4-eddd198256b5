package com.zkdm.iai.ai.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zkdm.iai.ai.config.AiConfig;
import com.zkdm.iai.ai.dto.ChatRequestDto;
import com.zkdm.iai.ai.service.IStreamChatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 流式聊天服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StreamChatServiceImpl implements IStreamChatService {

    private final AiConfig aiConfig;
    private final ObjectMapper objectMapper;
    private final WebClient aiWebClient;

    @Override
    public Flux<String> sendStreamMessage(ChatRequestDto request) {
        // 设置默认用户标识
        if (!StringUtils.hasText(request.getUser())) {
            request.setUser(aiConfig.getDefaultUser());
        }

        // 确保是流式模式
        request.setResponseMode("streaming");

        log.info("开始流式聊天，用户: {}, 会话ID: {}, 查询内容: {}",
            request.getUser(), request.getConversationId(), request.getQuery());

        // 构建请求体
        Map<String, Object> requestBody = buildRequestBody(request);

        return aiWebClient.post()
            .uri("/chat-messages")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.TEXT_EVENT_STREAM)
            .bodyValue(requestBody)
            .retrieve()
            .bodyToFlux(String.class)
            .timeout(Duration.ofMinutes(5))
            .filter(data -> data != null && !data.trim().isEmpty())
            .doOnNext(data -> log.debug("接收到流式数据: {}", data.length() > 100 ? data.substring(0, 100) + "..." : data))
            .doOnError(error -> log.error("流式聊天处理失败", error))
            .onErrorReturn("data: {\"event\":\"error\",\"error\":\"流式聊天处理失败\"}\n\n");
    }

    /**
     * 构建请求体
     */
    private Map<String, Object> buildRequestBody(ChatRequestDto request) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("query", request.getQuery());
        requestBody.put("response_mode", request.getResponseMode());
        requestBody.put("user", request.getUser());

        if (request.getInputs() != null) {
            requestBody.put("inputs", request.getInputs());
        } else {
            requestBody.put("inputs", new HashMap<>());
        }

        if (StringUtils.hasText(request.getConversationId())) {
            requestBody.put("conversation_id", request.getConversationId());
        } else {
            requestBody.put("conversation_id", "");
        }

        return requestBody;
    }


}
