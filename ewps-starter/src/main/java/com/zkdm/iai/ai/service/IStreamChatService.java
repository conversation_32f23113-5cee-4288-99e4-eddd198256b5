package com.zkdm.iai.ai.service;

import com.zkdm.iai.ai.dto.ChatRequestDto;
import com.zkdm.iai.ai.dto.StreamResponseDto;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * 流式聊天服务接口
 * 
 * <AUTHOR>
 */
public interface IStreamChatService {
    
    /**
     * 发送流式聊天消息
     * 
     * @param request 聊天请求参数
     * @return SSE发射器
     */
    SseEmitter sendStreamMessage(ChatRequestDto request);
}
