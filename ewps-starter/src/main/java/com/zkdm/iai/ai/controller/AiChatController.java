package com.zkdm.iai.ai.controller;

import com.zkdm.iai.ai.dto.ChatRequestDto;
import com.zkdm.iai.ai.dto.ConversationRenameDto;
import com.zkdm.iai.ai.service.IAiChatService;
import com.zkdm.iai.ai.service.IStreamChatService;
import com.zkdm.iai.ai.vo.ChatResponseVo;
import com.zkdm.iai.ai.vo.ConversationListVo;
import com.zkdm.iai.ai.vo.ConversationVo;
import com.zkdm.iai.ai.vo.MessageHistoryVo;
import com.zkdm.iai.core.domain.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;


/**
 * 类名 AiChatController
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/29
 */
@Slf4j
@Tag(name = "AI聊天服务", description = "AI大模型聊天对话相关接口")
@RestController
@RequestMapping("/api/ai/chat")
@RequiredArgsConstructor
@Validated
public class AiChatController {

    private final IAiChatService aiChatService;
    private final IStreamChatService streamChatService;


    /**
     * Send stream message sse emitter.
     *
     * @param request the request
     * @return the sse emitter
     */
    @Operation(summary = "发送流式聊天消息", description = "向AI大模型发送流式聊天消息，实时返回响应内容")
    @PostMapping(value = "/messages/stream", produces = "text/event-stream")
    public SseEmitter sendStreamMessage(
            @Parameter(description = "聊天请求参数", required = true)
            @Valid @RequestBody ChatRequestDto request) {
        try {
            return streamChatService.sendStreamMessage(request);
        } catch (Exception e) {
            log.error("发送流式聊天消息失败", e);
            SseEmitter emitter = new SseEmitter();
            try {
                emitter.send(SseEmitter.event()
                    .name("error")
                    .data("消息发送失败: " + e.getMessage()));
                emitter.complete();
            } catch (Exception ex) {
                emitter.completeWithError(ex);
            }
            return emitter;
        }
    }

    /**
     * Send message r.
     *
     * @param request the request
     * @return the r
     */
    @Operation(summary = "发送聊天消息（阻塞式）", description = "向AI大模型发送聊天消息，等待完整响应后返回")
    @PostMapping("/messages")
    public R<ChatResponseVo> sendMessage(
            @Parameter(description = "聊天请求参数", required = true)
            @Valid @RequestBody ChatRequestDto request) {
        try {
            // 强制设置为阻塞模式
            request.setResponseMode("blocking");
            ChatResponseVo response = aiChatService.sendMessage(request);
            return R.ok("消息发送成功", response);
        } catch (Exception e) {
            log.error("发送聊天消息失败", e);
            return R.fail("消息发送失败: " + e.getMessage());
        }
    }


    /**
     * Gets message history.
     *
     * @param user           the user
     * @param conversationId the conversation id
     * @return the message history
     */
    @Operation(summary = "获取消息历史记录", description = "获取指定用户和会话的消息历史记录")
    @GetMapping("/messages")
    public R<MessageHistoryVo> getMessageHistory(
            @Parameter(description = "用户标识", required = false)
            @RequestParam(required = false) String user,
            @Parameter(description = "会话ID", required = false)
            @RequestParam(value = "conversation_id", required = false) String conversationId) {
        try {
            MessageHistoryVo history = aiChatService.getMessageHistory(user, conversationId);
            return R.ok("获取消息历史成功", history);
        } catch (Exception e) {
            log.error("获取消息历史失败", e);
            return R.fail("获取消息历史失败: " + e.getMessage());
        }
    }

    /**
     * Gets conversation list.
     *
     * @param user the user
     * @return the conversation list
     */
    @Operation(summary = "获取会话列表", description = "获取指定用户的所有会话列表")
    @GetMapping("/conversations")
    public R<ConversationListVo> getConversationList(
            @Parameter(description = "用户标识", required = false)
            @RequestParam(required = false) String user) {
        try {
            ConversationListVo conversations = aiChatService.getConversationList(user);
            return R.ok("获取会话列表成功", conversations);
        } catch (Exception e) {
            log.error("获取会话列表失败", e);
            return R.fail("获取会话列表失败: " + e.getMessage());
        }
    }

    /**
     * Rename conversation r.
     *
     * @param conversationId the conversation id
     * @param request        the request
     * @return the r
     */
    @Operation(summary = "重命名会话", description = "修改指定会话的名称")
    @PostMapping("/conversations/{conversationId}/name")
    public R<ConversationVo> renameConversation(
            @Parameter(description = "会话ID", required = true)
            @PathVariable @NotBlank(message = "会话ID不能为空") String conversationId,
            @Parameter(description = "重命名请求参数", required = true)
            @Valid @RequestBody ConversationRenameDto request) {
        try {
            ConversationVo conversation = aiChatService.renameConversation(conversationId, request);
            return R.ok("会话重命名成功", conversation);
        } catch (Exception e) {
            log.error("会话重命名失败", e);
            return R.fail("会话重命名失败: " + e.getMessage());
        }
    }

    /**
     * Delete conversation r.
     *
     * @param conversationId the conversation id
     * @param user           the user
     * @return the r
     */
    @Operation(summary = "删除会话", description = "删除指定的会话及其所有消息")
    @DeleteMapping("/conversations/{conversationId}")
    public R<Boolean> deleteConversation(
            @Parameter(description = "会话ID", required = true)
            @PathVariable @NotBlank(message = "会话ID不能为空") String conversationId,
            @Parameter(description = "用户标识", required = false)
            @RequestParam(required = false) String user) {
        try {
            Boolean result = aiChatService.deleteConversation(conversationId, user);
            return R.ok("会话删除成功", result);
        } catch (Exception e) {
            log.error("会话删除失败", e);
            return R.fail("会话删除失败: " + e.getMessage());
        }
    }
}
