package com.zkdm.iai.ai.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 聊天请求DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "聊天请求参数")
public class ChatRequestDto {
    
    @Schema(description = "输入参数", example = "{}")
    private Map<String, Object> inputs;
    
    @NotBlank(message = "查询内容不能为空")
    @Schema(description = "用户查询内容", example = "请扮演一个经验丰富的大学辅导员，帮助学生解决一些大学生活中遇到的问题。", requiredMode = Schema.RequiredMode.REQUIRED)
    private String query;
    
    @Schema(description = "响应模式", example = "blocking", defaultValue = "blocking")
    @JsonProperty("response_mode")
    private String responseMode = "blocking";
    
    @Schema(description = "会话ID，用于继续之前的对话", example = "")
    @JsonProperty("conversation_id")
    private String conversationId;
    
    @Schema(description = "用户标识", example = "ewps-system", defaultValue = "ewps-system")
    private String user = "ewps-system";
}
