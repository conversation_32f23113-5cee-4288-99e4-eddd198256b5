package com.zkdm.iai.controller;

import com.zkdm.iai.core.domain.R;
import com.zkdm.iai.domain.vo.AcademicInfoVO;
import com.zkdm.iai.service.IAcademicWarningService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller: 用于处理学业相关的预警信息。
 */
@Tag(name = "学业预警", description = "用于获取学生学业预警信息的API")
@RestController
@RequestMapping("/api/v1/warnings/academic")
@RequiredArgsConstructor
public class AcademicWarningController {

    private final IAcademicWarningService academicWarningService;

    /**
     * GET /api/v1/warnings/academic/{studentId}
     * 获取指定学生的详细学业指标。
     *
     * @param studentId 学生的唯一ID（学号）。
     * @return 包含学生学业信息的响应实体。
     */
    @Operation(summary = "获取学生的详细学业指标",
               description = "获取指定学生的GPA、学分、获奖情况等多项学业指标。")
    @GetMapping("/{studentId}")
    public R<AcademicInfoVO> getAcademicInfo(
            @Parameter(description = "学生的唯一ID（学号）", required = true, example = "202310001")
            @PathVariable String studentId) {
        
        AcademicInfoVO academicInfo = academicWarningService.getAcademicInfo(studentId);
        return R.ok("学业信息获取成功", academicInfo);
    }
}