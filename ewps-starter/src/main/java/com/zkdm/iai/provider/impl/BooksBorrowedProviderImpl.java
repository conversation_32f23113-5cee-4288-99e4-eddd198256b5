package com.zkdm.iai.provider.impl;

import com.zkdm.iai.domain.entity.Library.BorrowingHistoryInfo;
import com.zkdm.iai.domain.vo.AcademicInfoVO;
import com.zkdm.iai.domain.vo.BooksBorrowedVO;
import com.zkdm.iai.mapper.library.BorrowingHistoryInfoMapper;
import com.zkdm.iai.mybatis.utils.MpQueryUtils;
import com.zkdm.iai.provider.AcademicInfoProvider;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.function.Consumer;

/**
 * 图书借阅数量信息提供者实现类
 */
@Component
@RequiredArgsConstructor
class BooksBorrowedProviderImpl implements AcademicInfoProvider {
    private final BorrowingHistoryInfoMapper borrowingMapper;
    @Override
    public Consumer<AcademicInfoVO.AcademicInfoVOBuilder> provide(String studentId) {
        // TODO: 真实实现应按当前学期的日期范围进行筛选
        var query = MpQueryUtils.<BorrowingHistoryInfo>lambdaQuery()
                .eq(BorrowingHistoryInfo::getStudentStaffId, studentId).build();
        long count = borrowingMapper.selectCount(query);
        // TODO: 与上学期对比的逻辑需要另外实现
        var booksBorrowedVO = BooksBorrowedVO.builder().count(count + "本").comparison("较上学期 -1本").build();
        return builder -> builder.booksBorrowedInfo(booksBorrowedVO);
    }
}