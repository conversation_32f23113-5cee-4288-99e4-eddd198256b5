package com.zkdm.iai.provider.impl;

import com.zkdm.iai.domain.entity.student.UndergraduateGpaInfo;
import com.zkdm.iai.domain.vo.AcademicInfoVO;
import com.zkdm.iai.domain.vo.GpaVO;
import com.zkdm.iai.mapper.student.UndergraduateGpaInfoMapper;
import com.zkdm.iai.mybatis.utils.MpQueryUtils;
import com.zkdm.iai.provider.AcademicInfoProvider;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.function.Consumer;

/**
 * GPA信息提供者实现类
 */
@Component
@RequiredArgsConstructor
class GpaProviderImpl implements AcademicInfoProvider {
    private final UndergraduateGpaInfoMapper gpaInfoMapper;
    @Override
    public Consumer<AcademicInfoVO.AcademicInfoVOBuilder> provide(String studentId) {
        var query = MpQueryUtils.<UndergraduateGpaInfo>lambdaQuery()
                .eq(UndergraduateGpaInfo::getStudentId, studentId)
                .orderByDesc(UndergraduateGpaInfo::getSemester)
                .build();
        Optional<UndergraduateGpaInfo> gpaInfoOpt = Optional.ofNullable(gpaInfoMapper.selectOne(query));
        GpaVO gpaVO = gpaInfoOpt.map(info -> GpaVO.builder()
                .currentGpa(info.getAverageGpa())
                .maxGpa("4")
                // TODO: 与上学期对比需要查询上学期的数据点，此处为模拟数据
                .comparison("较上学期 +10%")
                .build()
        ).orElseGet(() -> GpaVO.builder().currentGpa("0.0").maxGpa("4").comparison("暂无数据").build());
        return builder -> builder.gpaInfo(gpaVO);
    }
}