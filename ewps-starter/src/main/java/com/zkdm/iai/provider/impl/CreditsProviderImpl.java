package com.zkdm.iai.provider.impl;

import com.zkdm.iai.domain.entity.student.UndergraduateGpaInfo;
import com.zkdm.iai.domain.vo.*; // 引入所有VO类
import com.zkdm.iai.mapper.student.UndergraduateGpaInfoMapper;
import com.zkdm.iai.mybatis.utils.MpQueryUtils;
import com.zkdm.iai.provider.AcademicInfoProvider;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * 学分信息提供者实现类
 */
@Component
@RequiredArgsConstructor
class CreditsProviderImpl implements AcademicInfoProvider {

    private final UndergraduateGpaInfoMapper gpaInfoMapper;

    @Override
    public Consumer<AcademicInfoVO.AcademicInfoVOBuilder> provide(String studentId) {
        var query = MpQueryUtils.<UndergraduateGpaInfo>lambdaQuery()
                .eq(UndergraduateGpaInfo::getStudentId, studentId)
                .orderByDesc(UndergraduateGpaInfo::getSemester)
                .build();
        
        Optional<UndergraduateGpaInfo> gpaInfoOpt = Optional.ofNullable(gpaInfoMapper.selectOne(query));

        CreditsVO creditsVO = gpaInfoOpt.map(info -> {
            String rankPercentage = calculateRankPercentage(info.getRanking(), info.getTotalClassSize());
            return CreditsVO.builder()
                    .currentCredits(info.getTotalCredits()) 
                    // TODO: 毕业要求的总学分应来自培养方案或系统配置
                    .totalCredits("150")
                    .classRankPercentage(rankPercentage)
                    .build();
        }).orElseGet(() -> CreditsVO.builder()
                .currentCredits("0")
                .totalCredits("150")
                .classRankPercentage("暂无排名")
                .build());
        
        // 返回一个行为：将构建好的creditsVO设置到总Builder中
        return builder -> builder.creditsInfo(creditsVO);
    }

    private String calculateRankPercentage(String rankStr, String totalStr) {
        try {
            var rank = new BigDecimal(rankStr);
            var total = new BigDecimal(totalStr);
            if (total.compareTo(BigDecimal.ZERO) == 0) return "班级人数未知";
            var percentage = rank.divide(total, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
            return "班级前" + percentage.setScale(2, RoundingMode.HALF_UP) + "%";
        } catch (Exception e) {
            return "排名信息异常";
        }
    }
}
