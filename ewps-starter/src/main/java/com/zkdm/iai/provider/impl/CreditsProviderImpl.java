package com.zkdm.iai.provider.impl;

import com.zkdm.iai.domain.dto.StudentGpaInfoDto;
import com.zkdm.iai.domain.vo.*; // 引入所有VO类
import com.zkdm.iai.mapper.student.UndergraduateGpaInfoMapper;
import com.zkdm.iai.provider.AcademicInfoProvider;
import com.zkdm.iai.service.impl.MockGpaDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * 学分信息提供者实现类
 */
@Slf4j
@Component
@RequiredArgsConstructor
class CreditsProviderImpl implements AcademicInfoProvider {

    private final UndergraduateGpaInfoMapper gpaInfoMapper;
    private final MockGpaDataService mockGpaDataService;

    @Override
    public Consumer<AcademicInfoVO.AcademicInfoVOBuilder> provide(String studentId) {
        StudentGpaInfoDto gpaInfo = null;

        try {
            // 尝试从数据库查询
            gpaInfo = gpaInfoMapper.selectLatestGpaInfoWithBasicInfo(studentId);
        } catch (Exception e) {
            log.warn("从数据库查询GPA信息失败，使用模拟数据: {}", e.getMessage());
        }

        // 如果数据库查询失败，使用模拟数据
        if (gpaInfo == null) {
            gpaInfo = mockGpaDataService.getStudentGpaInfo(studentId);
        }

        final StudentGpaInfoDto finalGpaInfo = gpaInfo;
        CreditsVO creditsVO = Optional.ofNullable(finalGpaInfo).map(info -> {
            String rankPercentage = calculateRankPercentage(info.getRanking(), info.getTotalClassSize());
            return CreditsVO.builder()
                    .currentCredits(info.getTotalCredits())
                    // TODO: 毕业要求的总学分应来自培养方案或系统配置
                    .totalCredits("150")
                    .classRankPercentage(rankPercentage)
                    .build();
        }).orElseGet(() -> CreditsVO.builder()
                .currentCredits("0")
                .totalCredits("150")
                .classRankPercentage("暂无排名")
                .build());

        // 返回一个行为：将构建好的creditsVO设置到总Builder中
        return builder -> builder.creditsInfo(creditsVO);
    }

    private String calculateRankPercentage(String rankStr, String totalStr) {
        try {
            var rank = new BigDecimal(rankStr);
            var total = new BigDecimal(totalStr);
            if (total.compareTo(BigDecimal.ZERO) == 0) return "班级人数未知";
            var percentage = rank.divide(total, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
            return "班级前" + percentage.setScale(2, RoundingMode.HALF_UP) + "%";
        } catch (Exception e) {
            return "排名信息异常";
        }
    }
}
