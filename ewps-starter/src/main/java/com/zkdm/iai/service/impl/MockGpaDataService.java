package com.zkdm.iai.service.impl;

import com.zkdm.iai.domain.dto.StudentGpaInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 模拟GPA数据服务
 * 用于在真实GPA表不存在时提供测试数据
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class MockGpaDataService {
    
    private static final Map<String, StudentGpaInfoDto> MOCK_DATA = new HashMap<>();
    
    static {
        // 初始化模拟数据
        MOCK_DATA.put("202310001", StudentGpaInfoDto.builder()
            .studentId("202310001")
            .name("张三")
            .semester("2023-2024-2")
            .academicYear("2023-2024")
            .totalCredits("90.0")
            .averageGpa("3.85")
            .weightedAverageScore("85.6")
            .arithmeticAverageScore("84.2")
            .requiredCredits("60.0")
            .electiveCredits("30.0")
            .practicalCredits("15.0")
            .passedCourseCount("30")
            .failedCourseCount("1")
            .ranking("15")
            .totalClassSize("120")
            .majorCode("00001")
            .departmentCode("FD000020")
            .classCode("CS2023001")
            .grade("2023")
            .build());
            
        MOCK_DATA.put("202310002", StudentGpaInfoDto.builder()
            .studentId("202310002")
            .name("李四")
            .semester("2023-2024-2")
            .academicYear("2023-2024")
            .totalCredits("85.5")
            .averageGpa("3.72")
            .weightedAverageScore("84.1")
            .arithmeticAverageScore("83.5")
            .requiredCredits("56.0")
            .electiveCredits("29.5")
            .practicalCredits("12.0")
            .passedCourseCount("28")
            .failedCourseCount("2")
            .ranking("20")
            .totalClassSize("120")
            .majorCode("00001")
            .departmentCode("FD000020")
            .classCode("CS2023001")
            .grade("2023")
            .build());
            
        MOCK_DATA.put("202310003", StudentGpaInfoDto.builder()
            .studentId("202310003")
            .name("王五")
            .semester("2023-2024-1")
            .academicYear("2023-2024")
            .totalCredits("48.0")
            .averageGpa("3.95")
            .weightedAverageScore("88.2")
            .arithmeticAverageScore("87.6")
            .requiredCredits("32.0")
            .electiveCredits("16.0")
            .practicalCredits("10.0")
            .passedCourseCount("16")
            .failedCourseCount("0")
            .ranking("8")
            .totalClassSize("115")
            .majorCode("00002")
            .departmentCode("FD000021")
            .classCode("EE2023001")
            .grade("2023")
            .build());
            
        MOCK_DATA.put("202310004", StudentGpaInfoDto.builder()
            .studentId("202310004")
            .name("赵六")
            .semester("2023-2024-1")
            .academicYear("2023-2024")
            .totalCredits("40.5")
            .averageGpa("3.45")
            .weightedAverageScore("78.9")
            .arithmeticAverageScore("77.2")
            .requiredCredits("26.0")
            .electiveCredits("14.5")
            .practicalCredits("5.0")
            .passedCourseCount("13")
            .failedCourseCount("4")
            .ranking("35")
            .totalClassSize("115")
            .majorCode("00002")
            .departmentCode("FD000021")
            .classCode("EE2023001")
            .grade("2023")
            .build());
            
        MOCK_DATA.put("202310005", StudentGpaInfoDto.builder()
            .studentId("202310005")
            .name("钱七")
            .semester("2023-2024-1")
            .academicYear("2023-2024")
            .totalCredits("46.0")
            .averageGpa("3.78")
            .weightedAverageScore("83.7")
            .arithmeticAverageScore("82.9")
            .requiredCredits("30.5")
            .electiveCredits("15.5")
            .practicalCredits("7.0")
            .passedCourseCount("15")
            .failedCourseCount("1")
            .ranking("18")
            .totalClassSize("125")
            .majorCode("00001")
            .departmentCode("FD000020")
            .classCode("CS2023002")
            .grade("2023")
            .build());
    }
    
    /**
     * 获取学生GPA信息
     * 
     * @param studentId 学号
     * @return 学生GPA信息DTO
     */
    public StudentGpaInfoDto getStudentGpaInfo(String studentId) {
        StudentGpaInfoDto data = MOCK_DATA.get(studentId);
        if (data == null) {
            log.warn("未找到学号{}的模拟数据，返回默认数据", studentId);
            return createDefaultGpaInfo(studentId);
        }
        return data;
    }
    
    /**
     * 创建默认GPA信息
     * 
     * @param studentId 学号
     * @return 默认GPA信息
     */
    private StudentGpaInfoDto createDefaultGpaInfo(String studentId) {
        return StudentGpaInfoDto.builder()
            .studentId(studentId)
            .name("未知学生")
            .semester("2023-2024-2")
            .academicYear("2023-2024")
            .totalCredits("0.0")
            .averageGpa("0.0")
            .weightedAverageScore("0.0")
            .arithmeticAverageScore("0.0")
            .requiredCredits("0.0")
            .electiveCredits("0.0")
            .practicalCredits("0.0")
            .passedCourseCount("0")
            .failedCourseCount("0")
            .ranking("0")
            .totalClassSize("0")
            .majorCode("00000")
            .departmentCode("FD000000")
            .classCode("UNKNOWN")
            .grade("2023")
            .build();
    }
}
