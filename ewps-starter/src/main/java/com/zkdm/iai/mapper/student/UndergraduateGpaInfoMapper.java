package com.zkdm.iai.mapper.student;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zkdm.iai.domain.dto.StudentGpaInfoDto;
import com.zkdm.iai.domain.entity.student.UndergraduateGpaInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface UndergraduateGpaInfoMapper extends BaseMapper<UndergraduateGpaInfo> {

    /**
     * 根据学号查询学生基本信息（临时方案，直接从基本信息表获取）
     *
     * @param studentId 学号
     * @return 学生GPA信息DTO
     */
    @Select("""
        SELECT
            b.XH as studentId,
            b.XM as name,
            '2023-2024-2' as semester,
            '2023-2024' as academicYear,
            '90.0' as totalCredits,
            '3.85' as averageGpa,
            '85.6' as weightedAverageScore,
            '84.2' as arithmeticAverageScore,
            '60.0' as requiredCredits,
            '30.0' as electiveCredits,
            '15.0' as practicalCredits,
            '30' as passedCourseCount,
            '1' as failedCourseCount,
            '15' as ranking,
            '120' as totalClassSize,
            '00001' as majorCode,
            'FD000020' as departmentCode,
            'CS2023001' as classCode,
            '2023' as grade
        FROM T_GXXS_BKSJBXX b
        WHERE b.XH = #{studentId}
        LIMIT 1
        """)
    StudentGpaInfoDto selectLatestGpaInfoWithBasicInfo(@Param("studentId") String studentId);
}
