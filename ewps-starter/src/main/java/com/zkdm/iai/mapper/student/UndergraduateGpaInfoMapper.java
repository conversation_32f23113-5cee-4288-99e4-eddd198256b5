package com.zkdm.iai.mapper.student;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zkdm.iai.domain.dto.StudentGpaInfoDto;
import com.zkdm.iai.domain.entity.student.UndergraduateGpaInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface UndergraduateGpaInfoMapper extends BaseMapper<UndergraduateGpaInfo> {

    /**
     * 根据学号查询学生最新的GPA信息（包含基本信息）
     *
     * @param studentId 学号
     * @return 学生GPA信息DTO
     */
    @Select("""
        SELECT
            g.XH as studentId,
            b.XM as name,
            g.XQ as semester,
            g.XN as academicYear,
            g.ZXF as totalCredits,
            g.PJJD as averageGpa,
            g.JQPJF as weightedAverageScore,
            g.SSPJF as arithmeticAverageScore,
            g.BXKXF as requiredCredits,
            g.XXKXF as electiveCredits,
            g.SJKXF as practicalCredits,
            g.TGKCMS as passedCourseCount,
            g.BJGKCMS as failedCourseCount,
            g.PM as ranking,
            g.BJZRS as totalClassSize,
            g.ZYBM as majorCode,
            g.YXBM as departmentCode,
            g.BJBM as classCode,
            g.NJ as grade
        FROM T_GXXS_BKSQCJDXX g
        LEFT JOIN T_GXXS_BKSJBXX b ON g.XH = b.XH
        WHERE g.XH = #{studentId}
        ORDER BY g.XQ DESC
        LIMIT 1
        """)
    StudentGpaInfoDto selectLatestGpaInfoWithBasicInfo(@Param("studentId") String studentId);
}
