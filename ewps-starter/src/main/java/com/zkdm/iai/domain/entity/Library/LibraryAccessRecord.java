package com.zkdm.iai.domain.entity.Library;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实体类：图书馆普通通行记录信息 (T_GXGGFW_TSGPTTXJLXX)
 * 所属部门: 网络信息中心
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("T_GXGGFW_TSGPTTXJLXX")
public class LibraryAccessRecord {

    /**
     * 唯一标识 (主键)
     */
    @TableId(value = "WYBS", type = IdType.INPUT)
    private String id;

    /**
     * 人员GID (关联到人员信息表的唯一标识)
     */
    @TableField("GJD")
    private String gid;

    /**
     * 学号 (关联到本科生基本信息表的学号)
     */
    @TableField("XH")
    private String studentId;

    /**
     * 姓名 (关联到本科生基本信息表的姓名)
     */
    @TableField("XM")
    private String name;

    /**
     * 性别 (关联到本科生基本信息表的性别)
     */
    @TableField("XB")
    private String gender;

    /**
     * 院系 (关联到本科生基本信息表的院系)
     */
    @TableField("YX")
    private String department;

    /**
     * 专业 (关联到本科生基本信息表的专业)
     */
    @TableField("ZY")
    private String major;

    /**
     * 年级 (关联到本科生基本信息表的年级)
     */
    @TableField("NJ")
    private String grade;

    /**
     * 班级 (关联到本科生基本信息表的班级)
     */
    @TableField("BJ")
    private String className;

    /**
     * 校区名称 (关联到本科生基本信息表的校区)
     */
    @TableField("XQMC")
    private String campusName;

    /**
     * 进出方向码 (关联代码表: DM_GXGGFW_JCFXM)
     */
    @TableField("JCFXM")
    private String directionCode;

    /**
     * 通行照片
     */
    @TableField("TXZP")
    private String accessPhoto;

    /**
     * 人员类型 (关联代码表: DM_GXGGFW_RYLX)
     */
    @TableField("RYLX")
    private String personType;

    /**
     * 通行时间
     */
    @TableField("TXSJ")
    private String accessTime;

    /**
     * 通行结果 (关联代码表: DM_GXGGFW_TXJG)
     */
    @TableField("TXJG")
    private String accessResult;

    /**
     * 设备编码 (关联代码表: DM_GXGGFW_SBBM)
     */
    @TableField("SBBM")
    private String deviceCode;

    /**
     * 设备名称 (关联代码表: DM_GXGGFW_SBMC)
     */
    @TableField("SBMC")
    private String deviceName;

    /**
     * 通行类型 (关联代码表: DM_GXGGFW_TXLX)
     */
    @TableField("TXLX")
    private String accessType;

    /**
     * 时间戳
     */
    @TableField("TTIMESTAMP")
    private String timestamp;
}