package com.zkdm.iai.domain.entity.student;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实体类：本科生基本信息 (T_GXXS_BKSJBXX)
 * 所属部门: 教务处
 * <AUTHOR>
 */
@Schema(description = "本科生基本信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("T_GXXS_BKSJBXX")
public class UndergraduateBasicInfo {

    /**
     * 唯一标识 (主键)
     */
    @Schema(description = "唯一标识", example = "1001")
    @TableId(value = "WYBS", type = IdType.INPUT)
    private String id;

    /**
     * 学号 (唯一)
     */
    @Schema(description = "学号", example = "202310001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("XH")
    private String studentId;

    /**
     * 姓名
     */
    @Schema(description = "姓名", example = "张三", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("XM")
    private String name;

    /**
     * 外文姓名
     */
    @Schema(description = "外文姓名", example = "Zhang San")
    @TableField("WWXM")
    private String foreignName;

    /**
     * 性别码 (关联代码表: DM_XB_XBM)
     */
    @Schema(description = "性别码", example = "1", allowableValues = {"1", "2"})
    @TableField("XBM")
    private String genderCode;

    /**
     * 曾用名
     */
    @Schema(description = "曾用名", example = "张小三")
    @TableField("CYM")
    private String formerName;

    /**
     * 出生日期
     */
    @Schema(description = "出生日期", example = "2005-01-01", format = "date")
    @TableField("CSRQ")
    private String birthDate;

    /**
     * 民族码 (关联代码表: DM_XB_MZM)
     */
    @Schema(description = "民族码", example = "01")
    @TableField("MZM")
    private String ethnicityCode;

    /**
     * 政治面貌码 (关联代码表: DM_XB_ZZMMM)
     */
    @Schema(description = "政治面貌码", example = "01")
    @TableField("ZZMMM")
    private String politicalStatusCode;

    /**
     * 婚姻状况码 (关联代码表: DM_XB_HYZKM)
     */
    @Schema(description = "婚姻状况码", example = "10")
    @TableField("HYZKM")
    private String maritalStatusCode;

    /**
     * 健康状况码 (关联代码表: DM_XB_JKZKM)
     */
    @Schema(description = "健康状况码", example = "1")
    @TableField("JKZKM")
    private String healthStatusCode;

    /**
     * 身份证件类型码 (关联代码表: DM_XB_ZJLXM)
     */
    @Schema(description = "身份证件类型码", example = "1")
    @TableField("ZJLXM")
    private String idTypeCode;

    /**
     * 身份证件号码
     */
    @Schema(description = "身份证件号码", example = "340123200501010001")
    @TableField("ZJHM")
    private String idNumber;

    /**
     * 港澳台侨外码 (关联代码表: DM_XB_GATQWM)
     */
    @Schema(description = "港澳台侨外码", example = "1")
    @TableField("GATQWM")
    private String hkMacaoTaiwanForeignCode;

    /**
     * 国别地区码 (关联代码表: DM_XB_GBDQM)
     */
    @Schema(description = "国别地区码", example = "156")
    @TableField("GBDQM")
    private String countryRegionCode;

    /**
     * 籍贯省份码 (关联代码表: DM_XB_XZQHM)
     */
    @Schema(description = "籍贯省份码", example = "340000")
    @TableField("JGSFM")
    private String nativeProvinceCode;

    /**
     * 籍贯城市码 (关联代码表: DM_XB_XZQHM)
     */
    @Schema(description = "籍贯城市码", example = "340100")
    @TableField("JGCSM")
    private String nativeCityCode;

    /**
     * 出生地省份码 (关联代码表: DM_XB_XZQHM)
     */
    @Schema(description = "出生地省份码", example = "340000")
    @TableField("CSDSFM")
    private String birthProvinceCode;

    /**
     * 出生地城市码 (关联代码表: DM_XB_XZQHM)
     */
    @Schema(description = "出生地城市码", example = "340100")
    @TableField("CSDCSM")
    private String birthCityCode;

    /**
     * 户口所在地省份码 (关联代码表: DM_XB_XZQHM)
     */
    @Schema(description = "户口所在地省份码", example = "340000")
    @TableField("HKSZD_SFM")
    private String householdProvinceCode;

    /**
     * 户口所在地城市码 (关联代码表: DM_XB_XZQHM)
     */
    @Schema(description = "户口所在地城市码", example = "340100")
    @TableField("HKSZD_CSM")
    private String householdCityCode;

    /**
     * 户口性质码 (关联代码表: DM_XB_HKXZM)
     */
    @Schema(description = "户口性质码", example = "1")
    @TableField("HKXZM")
    private String householdTypeCode;

    /**
     * 特长
     */
    @Schema(description = "特长", example = "篮球、编程")
    @TableField("TC")
    private String specialties;

    /**
     * 血型码 (关联代码表: DM_XB_XXM)
     */
    @Schema(description = "血型码", example = "1", allowableValues = {"1", "2", "3", "4"})
    @TableField("XXM")
    private String bloodTypeCode;

    /**
     * 宗教信仰码 (关联代码表: DM_XB_ZJXYM)
     */
    @Schema(description = "宗教信仰码", example = "1")
    @TableField("ZJXYM")
    private String religiousBeliefCode;

    /**
     * 手机号码
     */
    @Schema(description = "手机号码", example = "13800138000", pattern = "^1[3-9]\\d{9}$")
    @TableField("SJHM")
    private String mobilePhone;

    /**
     * 电子邮箱
     */
    @Schema(description = "电子邮箱", example = "<EMAIL>", format = "email")
    @TableField("DZYX")
    private String email;

    /**
     * 通信地址
     */
    @Schema(description = "通信地址", example = "安徽省合肥市包河区金寨路96号")
    @TableField("TXDZ")
    private String address;

    /**
     * 邮政编码
     */
    @Schema(description = "邮政编码", example = "230026", pattern = "^\\d{6}$")
    @TableField("YZBM")
    private String postalCode;

    /**
     * 家庭电话
     */
    @Schema(description = "家庭电话", example = "0551-63602553")
    @TableField("JTDH")
    private String homePhone;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳", example = "2023-10-01 10:00:00", format = "date-time")
    @TableField("TTIMESTAMP")
    private String timestamp;

    /**
     * 学号SM4加密
     */
    @Schema(description = "学号SM4加密", hidden = true)
    @TableField("XHSM4")
    private String studentIdSm4;
}
