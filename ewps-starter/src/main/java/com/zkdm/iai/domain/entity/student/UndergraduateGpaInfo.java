package com.zkdm.iai.domain.entity.student;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实体类：本科生全程绩点信息 (T_GXXS_BKSQCJDXX)
 * 所属部门: 教务处
 * <AUTHOR>
 */
@Schema(description = "本科生全程绩点信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("T_GXXS_BKSQCJDXX")
public class UndergraduateGpaInfo {

    /**
     * 唯一标识 (主键)
     */
    @Schema(description = "唯一标识", example = "1001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableId(value = "WYBS", type = IdType.INPUT)
    private String id;

    /**
     * 学号
     */
    @Schema(description = "学号", example = "202310001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("XH")
    private String studentId;

    /**
     * 学期
     */
    @Schema(description = "学期", example = "2023-2024-1")
    @TableField("XQ")
    private String semester;

    /**
     * 学年
     */
    @Schema(description = "学年", example = "2023-2024")
    @TableField("XN")
    private String academicYear;

    /**
     * 总学分
     */
    @Schema(description = "总学分", example = "120.5", minimum = "0")
    @TableField("ZXF")
    private String totalCredits;

    /**
     * 平均绩点
     */
    @Schema(description = "平均绩点", example = "3.85", minimum = "0", maximum = "4")
    @TableField("PJJD")
    private String averageGpa;

    /**
     * 加权平均分
     */
    @Schema(description = "加权平均分", example = "85.6", minimum = "0", maximum = "100")
    @TableField("JQPJF")
    private String weightedAverageScore;

    /**
     * 算术平均分
     */
    @Schema(description = "算术平均分", example = "84.2", minimum = "0", maximum = "100")
    @TableField("SSPJF")
    private String arithmeticAverageScore;

    /**
     * 必修课学分
     */
    @Schema(description = "必修课学分", example = "90.0", minimum = "0")
    @TableField("BXKXF")
    private String requiredCredits;

    /**
     * 选修课学分
     */
    @Schema(description = "选修课学分", example = "30.5", minimum = "0")
    @TableField("XXKXF")
    private String electiveCredits;

    /**
     * 实践课学分
     */
    @Schema(description = "实践课学分", example = "15.0", minimum = "0")
    @TableField("SJKXF")
    private String practicalCredits;

    /**
     * 通过课程门数
     */
    @Schema(description = "通过课程门数", example = "25", minimum = "0")
    @TableField("TGKCMS")
    private String passedCourseCount;

    /**
     * 不及格课程门数
     */
    @Schema(description = "不及格课程门数", example = "2", minimum = "0")
    @TableField("BJGKCMS")
    private String failedCourseCount;

    /**
     * 排名
     */
    @Schema(description = "排名", example = "15", minimum = "1")
    @TableField("PM")
    private String ranking;

    /**
     * 班级总人数
     */
    @Schema(description = "班级总人数", example = "120", minimum = "1")
    @TableField("BJZRS")
    private String totalClassSize;

    /**
     * 专业编码
     */
    @Schema(description = "专业编码", example = "080901")
    @TableField("ZYBM")
    private String majorCode;

    /**
     * 院系编码
     */
    @Schema(description = "院系编码", example = "CS001")
    @TableField("YXBM")
    private String departmentCode;

    /**
     * 班级编码
     */
    @Schema(description = "班级编码", example = "CS2023-01")
    @TableField("BJBM")
    private String classCode;

    /**
     * 年级
     */
    @Schema(description = "年级", example = "2023")
    @TableField("NJ")
    private String grade;

    /**
     * 学生类别
     */
    @Schema(description = "学生类别", example = "普通本科生")
    @TableField("XSLB")
    private String studentCategory;

    /**
     * 培养层次
     */
    @Schema(description = "培养层次", example = "本科")
    @TableField("PYCC")
    private String educationLevel;

    /**
     * 学习形式
     */
    @Schema(description = "学习形式", example = "全日制")
    @TableField("XXXS")
    private String studyForm;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳", example = "2023-10-01 10:00:00", format = "date-time")
    @TableField("TTIMESTAMP")
    private String timestamp;

    /**
     * 学号SM4加密
     */
    @Schema(description = "学号SM4加密", hidden = true)
    @TableField("XHSM4")
    private String studentIdSm4;

    /**
     * 绩点计算方式
     */
    @Schema(description = "绩点计算方式", example = "标准4.0制")
    @TableField("JDJSFS")
    private String gpaCalculationMethod;

    /**
     * 是否有效
     */
    @Schema(description = "是否有效", example = "1", allowableValues = {"0", "1"})
    @TableField("SFYX")
    private String isValid;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2023-10-01 10:00:00", format = "date-time")
    @TableField("GXSJ")
    private String updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "正常")
    @TableField("BZ")
    private String remarks;

}
