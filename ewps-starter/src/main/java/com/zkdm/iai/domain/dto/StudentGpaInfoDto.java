package com.zkdm.iai.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 学生GPA信息DTO（包含基本信息）
 * 
 * <AUTHOR>
 */
@Schema(description = "学生GPA信息（包含基本信息）")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StudentGpaInfoDto {

    /**
     * 学号
     */
    @Schema(description = "学号", example = "202310001")
    private String studentId;

    /**
     * 姓名
     */
    @Schema(description = "姓名", example = "张三")
    private String name;

    /**
     * 学期
     */
    @Schema(description = "学期", example = "2023-2024-1")
    private String semester;

    /**
     * 学年
     */
    @Schema(description = "学年", example = "2023-2024")
    private String academicYear;

    /**
     * 总学分
     */
    @Schema(description = "总学分", example = "120.5")
    private String totalCredits;

    /**
     * 平均绩点
     */
    @Schema(description = "平均绩点", example = "3.85")
    private String averageGpa;

    /**
     * 加权平均分
     */
    @Schema(description = "加权平均分", example = "85.6")
    private String weightedAverageScore;

    /**
     * 算术平均分
     */
    @Schema(description = "算术平均分", example = "84.2")
    private String arithmeticAverageScore;

    /**
     * 必修课学分
     */
    @Schema(description = "必修课学分", example = "90.0")
    private String requiredCredits;

    /**
     * 选修课学分
     */
    @Schema(description = "选修课学分", example = "30.5")
    private String electiveCredits;

    /**
     * 实践课学分
     */
    @Schema(description = "实践课学分", example = "15.0")
    private String practicalCredits;

    /**
     * 通过课程门数
     */
    @Schema(description = "通过课程门数", example = "25")
    private String passedCourseCount;

    /**
     * 不及格课程门数
     */
    @Schema(description = "不及格课程门数", example = "2")
    private String failedCourseCount;

    /**
     * 排名
     */
    @Schema(description = "排名", example = "15")
    private String ranking;

    /**
     * 班级总人数
     */
    @Schema(description = "班级总人数", example = "120")
    private String totalClassSize;

    /**
     * 专业编码
     */
    @Schema(description = "专业编码", example = "080901")
    private String majorCode;

    /**
     * 院系编码
     */
    @Schema(description = "院系编码", example = "CS001")
    private String departmentCode;

    /**
     * 班级编码
     */
    @Schema(description = "班级编码", example = "CS2023-01")
    private String classCode;

    /**
     * 年级
     */
    @Schema(description = "年级", example = "2023")
    private String grade;
}
