package com.zkdm.iai.domain.entity.dict;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实体类：本科生专业码 (DM_XB_BKSZYM)
 * <AUTHOR>
 */
@Schema(description = "本科生专业信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("DM_XB_BKSZYM")
public class UndergraduateMajor {

    /**
     * 代码 (专业编码)
     */
    @Schema(description = "专业编码", example = "080901", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableId("ZYBM")
    private String majorCode;

    /**
     * 代码名称 (专业名称)
     */
    @Schema(description = "专业名称", example = "计算机科学与技术", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("ZYMC")
    private String majorName;

    /**
     * 学科门类编码
     */
    @Schema(description = "学科门类编码", example = "08")
    @TableField("XKMLBM")
    private String disciplineCategoryCode;

    /**
     * 学科门类名称
     */
    @Schema(description = "学科门类名称", example = "工学")
    @TableField("XKMLMC")
    private String disciplineCategoryName;

    /**
     * 是否启用 (1:是, 0:否)
     */
    @Schema(description = "是否启用", example = "true")
    @TableField("SFQY")
    private Boolean enabled;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "计算机相关专业")
    @TableField("BZ")
    private String remarks;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳", example = "2023-10-01 10:00:00", format = "date-time")
    @TableField("SJCS")
    private String timestamp;
}
