#!/usr/bin/env python3
"""
测试学业预警API的Python脚本
"""

import requests
import json

def test_academic_info_api():
    """测试学业信息获取接口"""
    base_url = "http://localhost:8080"
    student_ids = ["202310001", "202310002", "202310003", "202310004", "202310005"]
    
    print("=" * 60)
    print("测试学业预警API")
    print("=" * 60)

    # 先测试简化的测试接口
    print("\n🧪 测试简化接口:")
    for student_id in student_ids[:2]:  # 只测试前两个
        test_url = f"{base_url}/api/v1/warnings/academic/test/{student_id}"
        try:
            response = requests.get(test_url, timeout=10)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ {student_id}: {result.get('data', 'N/A')}")
            else:
                print(f"❌ {student_id}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {student_id}: {e}")

    print("\n📊 测试完整接口:")
    
    for student_id in student_ids:
        url = f"{base_url}/api/v1/warnings/academic/{student_id}"
        
        print(f"\n测试学号: {student_id}")
        print(f"请求URL: {url}")
        print("-" * 40)
        
        try:
            response = requests.get(url, timeout=30)
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 请求成功")
                print(f"响应消息: {result.get('message', 'N/A')}")
                
                # 显示学业信息摘要
                data = result.get('data', {})
                if data:
                    print("\n📊 学业信息摘要:")
                    
                    # GPA信息
                    gpa_info = data.get('gpaInfo', {})
                    if gpa_info:
                        print(f"  GPA: {gpa_info.get('currentGpa', 'N/A')}/{gpa_info.get('maxGpa', 'N/A')}")
                        print(f"  对比: {gpa_info.get('comparison', 'N/A')}")
                    
                    # 学分信息
                    credits_info = data.get('creditsInfo', {})
                    if credits_info:
                        print(f"  学分: {credits_info.get('currentCredits', 'N/A')}/{credits_info.get('totalCredits', 'N/A')}")
                        print(f"  排名: {credits_info.get('classRankPercentage', 'N/A')}")
                    
                    # 出勤信息
                    attendance_info = data.get('attendanceInfo', {})
                    if attendance_info:
                        print(f"  出勤率: {attendance_info.get('rate', 'N/A')}")
                    
                    # 获奖信息
                    awards_info = data.get('awardsInfo', {})
                    if awards_info:
                        print(f"  获奖: {awards_info.get('count', 'N/A')}")
                    
                    # 图书借阅
                    books_info = data.get('booksBorrowedInfo', {})
                    if books_info:
                        print(f"  借书: {books_info.get('count', 'N/A')}")
                    
                    # 图书馆出入
                    library_info = data.get('libraryAccessInfo', {})
                    if library_info:
                        print(f"  图书馆: {library_info.get('count', 'N/A')}")
                else:
                    print("⚠️  无学业数据")
                    
            else:
                print("❌ 请求失败")
                try:
                    error_info = response.json()
                    print(f"错误信息: {error_info.get('message', response.text)}")
                except:
                    print(f"错误信息: {response.text}")
                    
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
        except Exception as e:
            print(f"❌ 处理异常: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

def test_health_check():
    """测试健康检查接口"""
    url = "http://localhost:8080/actuator/health"
    
    print("\n🔍 健康检查:")
    print(f"请求URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            result = response.json()
            status = result.get('status', 'UNKNOWN')
            print(f"✅ 应用状态: {status}")
        else:
            print(f"⚠️  健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")

if __name__ == "__main__":
    # 先检查应用健康状态
    test_health_check()
    
    # 测试学业信息API
    test_academic_info_api()
