2025-07-31 09:24:02 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-31 09:24:02 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 11068 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-31 09:24:02 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-31 09:24:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 09:24:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 09:24:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
2025-07-31 09:24:04 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=05392083-818a-3fcf-948d-05036ee1e940
2025-07-31 09:24:04 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-31 09:24:04 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2477 ms
2025-07-31 09:24:06 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-31 09:24:06 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-07-31 09:24:06 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-07-31 09:24:06 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-07-31 09:24:08 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-31 09:24:08 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-31 09:24:08 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-31 09:24:08 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-31 09:24:08 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-31 09:24:08 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8081 (http) with context path '/'
2025-07-31 09:24:08 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.585 seconds (process running for 8.96)
2025-07-31 09:24:08 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8081/swagger-ui/index.html
2025-07-31 09:24:09 [RMI TCP Connection(1)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-31 09:24:09 [RMI TCP Connection(3)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 09:24:09 [RMI TCP Connection(3)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-31 09:24:09 [RMI TCP Connection(3)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-31 09:24:13 [XNIO-1 task-5] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1045 ms
2025-07-31 09:24:14 [RMI TCP Connection(1)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@f9ab7a
2025-07-31 09:24:14 [RMI TCP Connection(1)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-31 09:25:26 [XNIO-1 task-5] INFO  c.z.i.a.s.impl.AiChatServiceImpl - 发送聊天消息，用户: ewps-system, 会话ID: , 查询内容: 学生情绪不佳该如何解决？
2025-07-31 10:13:06 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-31 10:13:06 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 36196 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-31 10:13:06 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-31 10:13:07 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 10:13:07 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 10:13:07 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Redis repository interfaces.
2025-07-31 10:13:07 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=07258a08-5d54-3ead-a5cd-03abf1d878c4
2025-07-31 10:13:08 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-31 10:13:08 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2436 ms
2025-07-31 10:13:10 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-31 10:13:10 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-07-31 10:13:10 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-07-31 10:13:10 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-07-31 10:13:12 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-31 10:13:12 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-31 10:13:12 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-31 10:13:12 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-31 10:13:12 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-31 10:13:12 [main] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-31 10:13:12 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-31 10:15:39 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-31 10:15:39 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 22028 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-31 10:15:39 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-31 10:15:42 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 10:15:42 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 10:15:42 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 43 ms. Found 0 Redis repository interfaces.
2025-07-31 10:15:42 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=07258a08-5d54-3ead-a5cd-03abf1d878c4
2025-07-31 10:15:43 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-31 10:15:43 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3530 ms
2025-07-31 10:15:44 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-31 10:15:45 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-07-31 10:15:45 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-07-31 10:15:45 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-07-31 10:15:46 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-31 10:15:47 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-31 10:15:47 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-31 10:15:47 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-31 10:15:47 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-31 10:15:47 [main] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-31 10:15:47 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-31 10:16:02 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-31 10:16:02 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 32008 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-31 10:16:02 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-31 10:16:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 10:16:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 10:16:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Redis repository interfaces.
2025-07-31 10:16:04 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=07258a08-5d54-3ead-a5cd-03abf1d878c4
2025-07-31 10:16:04 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-31 10:16:04 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2488 ms
2025-07-31 10:16:06 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-31 10:16:06 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-07-31 10:16:06 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-07-31 10:16:06 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-07-31 10:16:08 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-31 10:16:08 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-31 10:16:08 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-31 10:16:08 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-31 10:16:08 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-31 10:16:08 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-31 10:16:08 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.477 seconds (process running for 7.471)
2025-07-31 10:16:08 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-07-31 10:16:09 [RMI TCP Connection(3)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 10:16:09 [RMI TCP Connection(3)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-31 10:16:09 [RMI TCP Connection(3)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-31 10:16:09 [RMI TCP Connection(2)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-31 10:16:12 [XNIO-1 task-4] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 992 ms
2025-07-31 10:16:13 [RMI TCP Connection(2)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@63b3b187
2025-07-31 10:16:13 [RMI TCP Connection(2)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-31 10:16:25 [XNIO-1 task-4] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: ewps-system, 会话ID: string, 查询内容: 请扮演一个经验丰富的大学辅导员，帮助学生解决一些大学生活中遇到的问题。
2025-07-31 10:24:48 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-31 10:24:48 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-31 10:24:48 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-31 10:24:48 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-31 10:24:48 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-31 10:24:48 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-31 10:24:58 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-31 10:24:58 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 40708 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-31 10:24:58 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-31 10:24:59 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 10:24:59 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 10:24:59 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
2025-07-31 10:25:00 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=07264997-8967-33d4-b996-4af8babf5a24
2025-07-31 10:25:00 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-31 10:25:00 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2276 ms
2025-07-31 10:25:02 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-31 10:25:02 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-07-31 10:25:02 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-07-31 10:25:02 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-07-31 10:25:04 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-31 10:25:04 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-31 10:25:04 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-31 10:25:04 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-31 10:25:04 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-31 10:25:04 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-31 10:25:04 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.421 seconds (process running for 7.309)
2025-07-31 10:25:04 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-07-31 10:25:05 [RMI TCP Connection(2)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 10:25:05 [RMI TCP Connection(2)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-31 10:25:05 [RMI TCP Connection(2)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-31 10:25:05 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-31 10:25:08 [XNIO-1 task-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1004 ms
2025-07-31 10:25:09 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@71e07016
2025-07-31 10:25:09 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-31 10:25:25 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: ewps-system, 会话ID: string, 查询内容: 请扮演一个经验丰富的大学辅导员，帮助学生解决一些大学生活中遇到的问题。
2025-07-31 10:28:28 [RMI TCP Connection(8)-***************] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: iai2, 会话ID: null, 查询内容: 大学生情绪不佳怎么办？
2025-07-31 10:28:34 [RMI TCP Connection(8)-***************] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: iai2, 会话ID: null, 查询内容: 大学生情绪不佳怎么办？
2025-07-31 10:28:44 [RMI TCP Connection(8)-***************] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: iai2, 会话ID: null, 查询内容: 大学生情绪不佳怎么办？
2025-07-31 10:29:00 [RMI TCP Connection(8)-***************] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: iai2, 会话ID: null, 查询内容: 大学生情绪不佳怎么办？
2025-07-31 10:29:46 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: flux-test-user, 会话ID: null, 查询内容: 111
2025-07-31 10:30:23 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: flux-test-user, 会话ID: null, 查询内容: 你好
2025-07-31 10:30:55 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: flux-test-user, 会话ID: null, 查询内容: 你好
2025-07-31 10:32:06 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: web-user, 会话ID: null, 查询内容: 你好
2025-07-31 10:36:49 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-31 10:36:49 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-31 10:36:49 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-31 10:36:49 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-31 10:36:51 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-31 10:36:51 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-31 10:36:56 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-31 10:36:56 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 40332 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-31 10:36:56 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-31 10:36:58 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 10:36:58 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 10:36:58 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Redis repository interfaces.
2025-07-31 10:36:58 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=07264997-8967-33d4-b996-4af8babf5a24
2025-07-31 10:36:59 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-31 10:36:59 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2354 ms
2025-07-31 10:37:00 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-31 10:37:01 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-07-31 10:37:01 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-07-31 10:37:01 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-07-31 10:37:02 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-31 10:37:02 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-31 10:37:02 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-31 10:37:02 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-31 10:37:02 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-31 10:37:02 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-31 10:37:03 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.677 seconds (process running for 7.423)
2025-07-31 10:37:03 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-07-31 10:37:03 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-31 10:37:03 [RMI TCP Connection(4)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 10:37:03 [RMI TCP Connection(4)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-31 10:37:03 [RMI TCP Connection(4)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-31 10:37:08 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@5ff979c8
2025-07-31 10:37:08 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-31 10:37:49 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: flux-test-user, 会话ID: null, 查询内容: 你好
2025-07-31 10:38:18 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: flux-test-user, 会话ID: d57a4423-ed39-41f1-ab54-3e1ba402f3c7, 查询内容: 大学生情绪不佳该如何处理？
2025-07-31 10:40:17 [XNIO-1 task-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1690 ms
2025-07-31 10:40:45 [XNIO-1 task-4] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: ewps-system, 会话ID: string, 查询内容: 请扮演一个经验丰富的大学辅导员，帮助学生解决一些大学生活中遇到的问题。
2025-07-31 10:45:03 [XNIO-1 task-4] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: ewps-system, 会话ID: string, 查询内容: 请扮演一个经验丰富的大学辅导员，帮助学生解决一些大学生活中遇到的问题。
2025-07-31 10:46:36 [XNIO-1 task-4] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: ewps-system, 会话ID: string, 查询内容: 请扮演一个经验丰富的大学辅导员，帮助学生解决一些大学生活中遇到的问题。
2025-07-31 10:46:55 [XNIO-1 task-4] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: ewps-system, 会话ID: string, 查询内容: 请扮演一个经验丰富的大学辅导员，帮助学生解决一些大学生活中遇到的问题。
2025-07-31 10:47:32 [XNIO-1 task-4] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: apifox-test-user, 会话ID: , 查询内容: 你好，请介绍一下你自己
2025-07-31 10:56:36 [XNIO-1 task-4] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: apifox-test-user, 会话ID: , 查询内容: 你好，请介绍一下你自己
2025-07-31 14:51:54 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-31 14:51:54 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-31 14:51:54 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-31 14:51:54 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-31 14:51:56 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-31 14:52:02 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-31 14:52:21 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-31 14:52:21 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 10032 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-31 14:52:21 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-31 14:52:23 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 14:52:23 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 14:52:23 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
2025-07-31 14:52:23 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=07264997-8967-33d4-b996-4af8babf5a24
2025-07-31 14:52:24 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-31 14:52:24 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2857 ms
2025-07-31 14:52:26 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-31 14:52:56 [redisson-netty-1-18] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-07-31 14:52:56 [redisson-netty-1-2] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-07-31 14:52:56 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-07-31 14:52:59 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-31 14:52:59 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-31 14:52:59 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-31 14:52:59 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-31 14:52:59 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-31 14:52:59 [main] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-31 14:52:59 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-31 14:53:18 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-31 14:53:18 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 38392 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-31 14:53:18 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-31 14:53:19 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 14:53:19 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 14:53:19 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-07-31 14:53:19 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=07264997-8967-33d4-b996-4af8babf5a24
2025-07-31 14:53:20 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-31 14:53:20 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2462 ms
2025-07-31 14:53:22 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-31 14:53:22 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-07-31 14:53:22 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-07-31 14:53:22 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-07-31 14:53:24 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-31 14:53:24 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-31 14:53:24 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-31 14:53:24 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-31 14:53:24 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-31 14:53:24 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-31 14:53:24 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.925 seconds (process running for 7.83)
2025-07-31 14:53:24 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-07-31 14:53:25 [RMI TCP Connection(1)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 14:53:25 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-31 14:53:25 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-31 14:53:25 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-31 14:53:28 [XNIO-1 task-5] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1086 ms
2025-07-31 14:53:30 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@1fb3fbe6
2025-07-31 14:53:30 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-31 15:03:08 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-31 15:03:08 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-31 15:03:08 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-31 15:03:08 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-31 15:03:08 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-31 15:03:08 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
