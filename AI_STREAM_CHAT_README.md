# AI流式聊天接口使用说明

## 概述

本项目已将AI对话接口改为默认流式输出，提供实时的聊天体验。用户可以看到AI逐字逐句地生成回复，而不需要等待完整响应。

## 接口变更

### 1. 默认响应模式变更
- **原来**: `response_mode` 默认为 `"blocking"`
- **现在**: `response_mode` 默认为 `"streaming"`

### 2. 新增流式聊天接口

#### 流式聊天接口（推荐）
```
POST /api/ai/chat/messages/stream
Content-Type: application/json
Accept: text/event-stream
```

**请求体示例:**
```json
{
    "query": "你好，请介绍一下你自己",
    "response_mode": "streaming",
    "user": "test-user",
    "conversation_id": ""
}
```

**响应格式:** Server-Sent Events (SSE)

**响应示例:**
```
data: {"event":"message","answer":"我是","message_id":"xxx","conversation_id":"yyy","created_at":1234567890}

data: {"event":"message","answer":"一个","message_id":"xxx","conversation_id":"yyy","created_at":1234567890}

data: {"event":"message","answer":"AI助手","message_id":"xxx","conversation_id":"yyy","created_at":1234567890}

data: {"event":"workflow_finished","finished":true,"data":{...完整元数据...}}
```

#### 阻塞式聊天接口（兼容）
```
POST /api/ai/chat/messages
Content-Type: application/json
```

此接口保持原有行为，等待完整响应后返回。

### 3. 测试接口

#### 流式测试接口
```
GET /api/ai/test/quick-stream-chat?message=你好
Accept: text/event-stream
```

#### 阻塞式测试接口
```
GET /api/ai/test/quick-chat?message=你好
```

## 使用方法

### 1. 前端JavaScript示例

```javascript
async function sendStreamMessage(message) {
    const response = await fetch('/api/ai/chat/messages/stream', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream'
        },
        body: JSON.stringify({
            query: message,
            response_mode: 'streaming',
            user: 'web-user'
        })
    });

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let aiResponse = '';

    while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });

        while (buffer.includes('\n\n')) {
            const index = buffer.indexOf('\n\n');
            const event = buffer.substring(0, index);
            buffer = buffer.substring(index + 2);

            if (event.startsWith('data:')) {
                const eventData = event.substring(5).trim();
                if (eventData) {
                    const data = JSON.parse(eventData);
                    
                    if (data.event === 'message' && data.answer) {
                        aiResponse += data.answer;
                        // 更新UI显示
                        updateChatMessage(aiResponse);
                    }
                    
                    if (data.event === 'workflow_finished') {
                        // 流式响应完成
                        console.log('响应完成');
                        break;
                    }
                }
            }
        }
    }
}
```

### 2. Python客户端示例

```python
import requests
import json

def stream_chat(message):
    url = "http://localhost:8081/api/ai/chat/messages/stream"
    
    payload = {
        "query": message,
        "response_mode": "streaming",
        "user": "python-client"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "text/event-stream"
    }
    
    response = requests.post(url, headers=headers, json=payload, stream=True)
    
    buffer = ""
    for chunk in response.iter_content(chunk_size=1024):
        if chunk:
            buffer += chunk.decode('utf-8')
            
            while "\n\n" in buffer:
                event, buffer = buffer.split("\n\n", 1)
                if event.startswith("data:"):
                    event_data = event[5:].strip()
                    if event_data:
                        data = json.loads(event_data)
                        if data.get("event") == "message":
                            print(data.get("answer", ""), end="", flush=True)
                        elif data.get("event") == "workflow_finished":
                            print("\n响应完成")
                            return
```

### 3. 测试页面

访问 `http://localhost:8081/stream-test.html` 可以使用内置的测试页面进行流式聊天测试。

## 技术实现

### 架构变更
1. **新增服务层**: `IStreamChatService` 和 `StreamChatServiceImpl`
2. **HTTP客户端**: 使用原生 `HttpURLConnection` 替代 Feign 处理流式响应
3. **SSE支持**: 使用 Spring 的 `SseEmitter` 实现服务端推送
4. **异步处理**: 使用 `CompletableFuture` 异步处理流式响应

### 关键特性
- **实时响应**: 逐字逐句显示AI回复
- **连接管理**: 自动处理连接超时和错误
- **兼容性**: 保持原有阻塞式接口不变
- **错误处理**: 完善的异常处理和错误提示

## 配置说明

在 `application.yml` 中的相关配置：

```yaml
ai:
  chat:
    base-url: http://**************:82/v1
    api-key: app-Rb5qu3b8pihdp6t56h0XXEAy
    connect-timeout: 30000
    read-timeout: 60000
    default-user: ewps-system
```

## 注意事项

1. **超时设置**: 流式响应的超时时间设置为5分钟
2. **错误处理**: 网络错误会自动关闭连接并提示用户
3. **浏览器兼容**: 现代浏览器都支持 Server-Sent Events
4. **并发限制**: 建议控制同时进行的流式连接数量

## 测试方法

1. **启动应用**: `mvn spring-boot:run`
2. **访问测试页面**: `http://localhost:8081/stream-test.html`
3. **运行Python测试**: `python test-stream.py`
4. **API文档**: `http://localhost:8081/swagger-ui.html`

## 故障排除

### 常见问题

1. **连接超时**: 检查网络连接和AI服务状态
2. **解析错误**: 确认AI服务返回的数据格式正确
3. **CORS问题**: 已在 `MvcConfiguration` 中配置跨域支持

### 日志查看

应用会输出详细的日志信息，包括：
- 请求开始和结束
- 流式数据接收过程
- 错误信息和异常堆栈

查看日志可以帮助诊断问题。
