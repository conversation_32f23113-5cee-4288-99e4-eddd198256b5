#!/usr/bin/env python3
"""
测试流式AI聊天接口的Python脚本
"""

import requests
import json
import time

def test_stream_chat():
    """测试流式聊天接口"""
    url = "http://localhost:8080/api/ai/chat/messages/stream"
    
    payload = {
        "query": "你好，请介绍一下你自己",
        "response_mode": "streaming",
        "user": "test-user"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "text/event-stream"
    }
    
    print("开始测试流式聊天接口...")
    print(f"请求URL: {url}")
    print(f"请求数据: {json.dumps(payload, ensure_ascii=False)}")
    print("-" * 50)
    
    try:
        response = requests.post(
            url,
            headers=headers,
            json=payload,
            stream=True,
            timeout=60
        )
        
        if response.status_code != 200:
            print(f"请求失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            return
        
        print("开始接收流式响应...")
        buffer = ""
        
        for chunk in response.iter_content(chunk_size=1024):
            if chunk:
                decoded_chunk = chunk.decode('utf-8')
                buffer += decoded_chunk
                
                # 处理SSE消息
                while "\n\n" in buffer:
                    event, buffer = buffer.split("\n\n", 1)
                    if event.startswith("data:"):
                        event_data = event[5:].strip()
                        if event_data:
                            try:
                                json_data = json.loads(event_data)
                                if json_data.get("event") == "message":
                                    print(f"收到消息: {json_data.get('answer', '')}", end="", flush=True)
                                elif json_data.get("event") == "workflow_finished":
                                    print("\n\n流式响应完成")
                                    print(f"完整元数据: {json.dumps(json_data, indent=2, ensure_ascii=False)}")
                                    return
                            except json.JSONDecodeError as e:
                                print(f"\n解析JSON失败: {e}")
                                print(f"原始数据: {event_data}")
        
        print("\n流式响应结束")
        
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
    except KeyboardInterrupt:
        print("\n用户中断")

def test_blocking_chat():
    """测试阻塞式聊天接口"""
    url = "http://localhost:8080/api/ai/chat/messages"
    
    payload = {
        "query": "你好，请简单介绍一下你自己",
        "response_mode": "blocking",
        "user": "test-user"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    print("\n" + "=" * 50)
    print("开始测试阻塞式聊天接口...")
    print(f"请求URL: {url}")
    print(f"请求数据: {json.dumps(payload, ensure_ascii=False)}")
    print("-" * 50)
    
    try:
        response = requests.post(
            url,
            headers=headers,
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            print("阻塞式响应成功:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"请求失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")

if __name__ == "__main__":
    print("AI聊天接口测试脚本")
    print("=" * 50)
    
    # 测试流式接口
    test_stream_chat()
    
    # 等待一下
    time.sleep(2)
    
    # 测试阻塞式接口
    test_blocking_chat()
