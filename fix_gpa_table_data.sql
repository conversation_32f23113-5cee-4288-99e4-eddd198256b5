-- 修复GPA表数据的SQL脚本
-- 为T_GXXS_BKSQCJDXX表添加测试数据

-- 首先确保基本信息表有数据
INSERT IGNORE INTO T_GXXS_BKSJBXX (WYBS, XH, XM, XBM, TTIMESTAMP) VALUES
('001', '202310001', '张三', '1', '20241220120000'),
('002', '202310002', '李四', '2', '20241220120000'),
('003', '202310003', '王五', '1', '20241220120000'),
('004', '202310004', '赵六', '2', '20241220120000'),
('005', '202310005', '钱七', '1', '20241220120000');

-- 为T_GXXS_BKSQCJDXX表添加测试数据（注意：这个表没有XM字段）
INSERT IGNORE INTO T_GXXS_BKSQCJDXX (
    WYBS, XH, XQ, XN, ZXF, PJJD, JQPJF, SSPJF, 
    BXKXF, XXKXF, SJKXF, TGKCMS, BJGKCMS, PM, BJZRS,
    ZYBM, YXBM, BJBM, NJ, XSLB, PYCC, XXXS, 
    TTIMESTAMP, JDJSFS, SFYX, GXSJ
) VALUES
-- 张三的GPA记录
('GPA001', '202310001', '2023-2024-1', '2023-2024', '45.5', '3.85', '85.6', '84.2', 
 '30.0', '15.5', '8.0', '15', '2', '15', '120',
 '00001', 'FD000020', 'CS2023001', '2023', '普通本科生', '本科', '全日制',
 '20241220120000', '标准4.0制', '1', '20241220120000'),

('GPA002', '202310001', '2024-2024-2', '2023-2024', '90.0', '3.92', '87.3', '86.1', 
 '60.0', '30.0', '15.0', '30', '1', '12', '120',
 '00001', 'FD000020', 'CS2023001', '2023', '普通本科生', '本科', '全日制',
 '20241220120000', '标准4.0制', '1', '20241220120000'),

-- 李四的GPA记录
('GPA003', '202310002', '2023-2024-1', '2023-2024', '42.0', '3.65', '82.4', '81.8', 
 '28.0', '14.0', '6.0', '14', '3', '25', '120',
 '00001', 'FD000020', 'CS2023001', '2023', '普通本科生', '本科', '全日制',
 '20241220120000', '标准4.0制', '1', '20241220120000'),

('GPA004', '202310002', '2024-2024-2', '2023-2024', '85.5', '3.72', '84.1', '83.5', 
 '56.0', '29.5', '12.0', '28', '2', '20', '120',
 '00001', 'FD000020', 'CS2023001', '2023', '普通本科生', '本科', '全日制',
 '20241220120000', '标准4.0制', '1', '20241220120000'),

-- 王五的GPA记录
('GPA005', '202310003', '2023-2024-1', '2023-2024', '48.0', '3.95', '88.2', '87.6', 
 '32.0', '16.0', '10.0', '16', '0', '8', '115',
 '00002', 'FD000021', 'EE2023001', '2023', '普通本科生', '本科', '全日制',
 '20241220120000', '标准4.0制', '1', '20241220120000'),

-- 赵六的GPA记录
('GPA006', '202310004', '2023-2024-1', '2023-2024', '40.5', '3.45', '78.9', '77.2', 
 '26.0', '14.5', '5.0', '13', '4', '35', '115',
 '00002', 'FD000021', 'EE2023001', '2023', '普通本科生', '本科', '全日制',
 '20241220120000', '标准4.0制', '1', '20241220120000'),

-- 钱七的GPA记录
('GPA007', '202310005', '2023-2024-1', '2023-2024', '46.0', '3.78', '83.7', '82.9', 
 '30.5', '15.5', '7.0', '15', '1', '18', '125',
 '00001', 'FD000020', 'CS2023002', '2023', '普通本科生', '本科', '全日制',
 '20241220120000', '标准4.0制', '1', '20241220120000');

-- 验证数据插入
SELECT 
    g.XH as student_id,
    b.XM as name,
    g.XQ as semester,
    g.PJJD as gpa,
    g.ZXF as total_credits,
    g.PM as ranking,
    g.BJZRS as class_size
FROM T_GXXS_BKSQCJDXX g
LEFT JOIN T_GXXS_BKSJBXX b ON g.XH = b.XH
ORDER BY g.XH, g.XQ DESC;
