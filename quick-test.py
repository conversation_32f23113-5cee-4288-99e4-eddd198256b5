#!/usr/bin/env python3
"""
快速测试学业预警API
"""

import requests

def quick_test():
    """快速测试"""
    base_url = "http://localhost:8080"
    
    print("🚀 快速测试学业预警API")
    print("=" * 40)
    
    # 测试应用健康状态
    try:
        health_url = f"{base_url}/actuator/health"
        response = requests.get(health_url, timeout=5)
        if response.status_code == 200:
            print("✅ 应用运行正常")
        else:
            print(f"⚠️  应用状态异常: {response.status_code}")
    except:
        print("❌ 应用未启动或无法访问")
        return
    
    # 测试简化接口
    student_id = "202310001"
    test_url = f"{base_url}/api/v1/warnings/academic/test/{student_id}"
    
    print(f"\n🧪 测试学号: {student_id}")
    print(f"📍 URL: {test_url}")
    
    try:
        response = requests.get(test_url, timeout=10)
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 请求成功!")
            print(f"📝 响应: {result.get('message', 'N/A')}")
            print(f"📋 数据:")
            print(result.get('data', 'N/A'))
        else:
            print("❌ 请求失败")
            try:
                error = response.json()
                print(f"🚨 错误: {error.get('message', response.text)}")
            except:
                print(f"🚨 错误: {response.text}")
                
    except Exception as e:
        print(f"❌ 异常: {e}")
    
    print("\n" + "=" * 40)
    print("测试完成")

if __name__ == "__main__":
    quick_test()
